class LaserSystem {
    constructor() {
        this.canvas = document.getElementById('renderCanvas');
        this.engine = new BABYLON.Engine(this.canvas, true);
        this.scene = null;
        this.camera = null;
        this.lasers = [];
        this.targetObjects = [];
        this.isAddingLaser = false;
        
        this.init();
        this.setupUI();
        this.render();
    }
    
    init() {
        // Scene erstellen
        this.scene = new BABYLON.Scene(this.engine);
        this.scene.clearColor = new BABYLON.Color3(0.02, 0.02, 0.1);
        
        // Kamera
        this.camera = new BABYLON.ArcRotateCamera(
            "camera", 
            -Math.PI / 2, 
            Math.PI / 2.5, 
            15, 
            BABYLON.Vector3.Zero(), 
            this.scene
        );
        this.camera.attachControl(this.canvas, true);
        this.camera.setTarget(BABYLON.Vector3.Zero());
        
        // Beleuchtung
        const light = new BABYLON.HemisphericLight("light", new BABYLON.Vector3(0, 1, 0), this.scene);
        light.intensity = 0.7;
        
        // Zusätzliches direktionales Licht
        const dirLight = new BABYLON.DirectionalLight("dirLight", new BABYLON.Vector3(-1, -1, -1), this.scene);
        dirLight.intensity = 0.5;
        
        this.createTargetObjects();
        this.setupClickHandler();
    }
    
    createTargetObjects() {
        // Verschiedene Zielobjekte erstellen
        const sphere = BABYLON.MeshBuilder.CreateSphere("sphere", {diameter: 2}, this.scene);
        sphere.position = new BABYLON.Vector3(-3, 0, 0);
        sphere.material = new BABYLON.StandardMaterial("sphereMat", this.scene);
        sphere.material.diffuseColor = new BABYLON.Color3(0.8, 0.2, 0.2);
        sphere.material.specularColor = new BABYLON.Color3(0.5, 0.5, 0.5);
        
        const box = BABYLON.MeshBuilder.CreateBox("box", {size: 2}, this.scene);
        box.position = new BABYLON.Vector3(3, 0, 0);
        box.material = new BABYLON.StandardMaterial("boxMat", this.scene);
        box.material.diffuseColor = new BABYLON.Color3(0.2, 0.8, 0.2);
        box.material.specularColor = new BABYLON.Color3(0.5, 0.5, 0.5);
        
        const cylinder = BABYLON.MeshBuilder.CreateCylinder("cylinder", {height: 3, diameter: 1.5}, this.scene);
        cylinder.position = new BABYLON.Vector3(0, 0, -3);
        cylinder.material = new BABYLON.StandardMaterial("cylinderMat", this.scene);
        cylinder.material.diffuseColor = new BABYLON.Color3(0.2, 0.2, 0.8);
        cylinder.material.specularColor = new BABYLON.Color3(0.5, 0.5, 0.5);
        
        const torus = BABYLON.MeshBuilder.CreateTorus("torus", {diameter: 2, thickness: 0.5}, this.scene);
        torus.position = new BABYLON.Vector3(0, 0, 3);
        torus.material = new BABYLON.StandardMaterial("torusMat", this.scene);
        torus.material.diffuseColor = new BABYLON.Color3(0.8, 0.8, 0.2);
        torus.material.specularColor = new BABYLON.Color3(0.5, 0.5, 0.5);
        
        // Boden
        const ground = BABYLON.MeshBuilder.CreateGround("ground", {width: 20, height: 20}, this.scene);
        ground.position.y = -2;
        ground.material = new BABYLON.StandardMaterial("groundMat", this.scene);
        ground.material.diffuseColor = new BABYLON.Color3(0.1, 0.1, 0.1);
        ground.material.specularColor = new BABYLON.Color3(0.1, 0.1, 0.1);
        
        this.targetObjects = [sphere, box, cylinder, torus, ground];
    }
    
    setupClickHandler() {
        this.scene.onPointerObservable.add((pointerInfo) => {
            if (pointerInfo.pickInfo.hit && this.isAddingLaser) {
                const hitPoint = pointerInfo.pickInfo.pickedPoint;
                const hitMesh = pointerInfo.pickInfo.pickedMesh;
                
                if (this.targetObjects.includes(hitMesh)) {
                    this.createLaser(hitPoint, hitMesh);
                    this.isAddingLaser = false;
                    document.getElementById('addLaserBtn').textContent = 'Laser hinzufügen';
                }
            }
        });
    }
    
    createLaser(targetPoint, targetMesh) {
        const laserColor = document.getElementById('laserColor').value;
        const intensity = parseFloat(document.getElementById('laserIntensity').value);
        
        // Laser-Startpunkt (etwas über dem Ziel)
        const startPoint = targetPoint.add(new BABYLON.Vector3(
            (Math.random() - 0.5) * 10,
            5 + Math.random() * 5,
            (Math.random() - 0.5) * 10
        ));
        
        // Laser-Linie erstellen
        const laserLine = BABYLON.MeshBuilder.CreateLines("laser", {
            points: [startPoint, targetPoint]
        }, this.scene);
        
        // Laser-Material und Farbe
        const color = this.getColorFromName(laserColor);
        laserLine.color = color;
        
        // Glüh-Effekt
        const laserGlow = BABYLON.MeshBuilder.CreateCylinder("laserGlow", {
            height: BABYLON.Vector3.Distance(startPoint, targetPoint),
            diameter: 0.1 * intensity
        }, this.scene);

        // Position und Rotation des Glüh-Effekts
        const direction = targetPoint.subtract(startPoint).normalize();
        const center = startPoint.add(targetPoint).scale(0.5);
        laserGlow.position = center;

        // Korrekte Ausrichtung der Tube entlang der Linie
        const up = new BABYLON.Vector3(0, 1, 0);
        const right = BABYLON.Vector3.Cross(direction, up).normalize();
        const correctedUp = BABYLON.Vector3.Cross(right, direction).normalize();

        laserGlow.rotation = BABYLON.Vector3.Zero();
        laserGlow.lookAt(center.add(direction), correctedUp);
        
        // Glüh-Material
        const glowMaterial = new BABYLON.StandardMaterial("glowMat", this.scene);
        glowMaterial.emissiveColor = color;
        glowMaterial.alpha = 0.6 * intensity;
        laserGlow.material = glowMaterial;
        
        // Auftreffpunkt-Effekt
        const hitEffect = BABYLON.MeshBuilder.CreateSphere("hitEffect", {diameter: 0.3}, this.scene);
        hitEffect.position = targetPoint;
        hitEffect.material = new BABYLON.StandardMaterial("hitMat", this.scene);
        hitEffect.material.emissiveColor = color;
        hitEffect.material.alpha = 0.8;
        
        // Animation für den Auftreffpunkt
        const animationKeys = [];
        animationKeys.push({frame: 0, value: 0.3});
        animationKeys.push({frame: 30, value: 0.5});
        animationKeys.push({frame: 60, value: 0.3});
        
        const scaleAnimation = new BABYLON.Animation("scaleAnimation", "scaling", 30, BABYLON.Animation.ANIMATIONTYPE_VECTOR3, BABYLON.Animation.ANIMATIONLOOPMODE_CYCLE);
        scaleAnimation.setKeys(animationKeys.map(key => ({...key, value: new BABYLON.Vector3(key.value, key.value, key.value)})));
        
        hitEffect.animations.push(scaleAnimation);
        this.scene.beginAnimation(hitEffect, 0, 60, true);
        
        // Laser-Objekt speichern
        const laser = {
            id: Date.now(),
            color: laserColor,
            intensity: intensity,
            startPoint: startPoint,
            targetPoint: targetPoint,
            targetMesh: targetMesh,
            line: laserLine,
            glow: laserGlow,
            hitEffect: hitEffect
        };
        
        this.lasers.push(laser);
        this.updateLaserList();
    }
    
    getColorFromName(colorName) {
        const colors = {
            red: new BABYLON.Color3(1, 0, 0),
            green: new BABYLON.Color3(0, 1, 0),
            blue: new BABYLON.Color3(0, 0, 1),
            yellow: new BABYLON.Color3(1, 1, 0),
            purple: new BABYLON.Color3(1, 0, 1)
        };
        return colors[colorName] || colors.red;
    }
    
    removeLaser(laserId) {
        const laserIndex = this.lasers.findIndex(laser => laser.id === laserId);
        if (laserIndex !== -1) {
            const laser = this.lasers[laserIndex];
            laser.line.dispose();
            laser.glow.dispose();
            laser.hitEffect.dispose();
            this.lasers.splice(laserIndex, 1);
            this.updateLaserList();
        }
    }
    
    clearAllLasers() {
        this.lasers.forEach(laser => {
            laser.line.dispose();
            laser.glow.dispose();
            laser.hitEffect.dispose();
        });
        this.lasers = [];
        this.updateLaserList();
    }
    
    updateLaserList() {
        const laserList = document.getElementById('laserList');
        
        if (this.lasers.length === 0) {
            laserList.innerHTML = '<div style="text-align: center; color: #666;">Keine Laser aktiv</div>';
            return;
        }
        
        laserList.innerHTML = this.lasers.map(laser => `
            <div class="laser-item">
                <span>🔴 ${laser.color} (${laser.intensity}x)</span>
                <button onclick="laserSystem.removeLaser(${laser.id})">Löschen</button>
            </div>
        `).join('');
    }
    
    setupUI() {
        // Intensitäts-Slider
        const intensitySlider = document.getElementById('laserIntensity');
        const intensityValue = document.getElementById('intensityValue');
        
        intensitySlider.addEventListener('input', (e) => {
            intensityValue.textContent = e.target.value;
        });
        
        // Laser hinzufügen Button
        document.getElementById('addLaserBtn').addEventListener('click', () => {
            this.isAddingLaser = !this.isAddingLaser;
            const btn = document.getElementById('addLaserBtn');
            btn.textContent = this.isAddingLaser ? 'Klicken Sie auf ein Objekt...' : 'Laser hinzufügen';
        });
        
        // Alle löschen Button
        document.getElementById('clearAllBtn').addEventListener('click', () => {
            this.clearAllLasers();
        });
    }
    
    render() {
        this.engine.runRenderLoop(() => {
            this.scene.render();
        });
        
        window.addEventListener('resize', () => {
            this.engine.resize();
        });
    }
}

// Anwendung starten
let laserSystem;
window.addEventListener('DOMContentLoaded', () => {
    laserSystem = new LaserSystem();
});
