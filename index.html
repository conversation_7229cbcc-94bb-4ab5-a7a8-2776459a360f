<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Babylon.js Laser System</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
        }
        
        #renderCanvas {
            width: 100%;
            height: 100vh;
            display: block;
            outline: none;
        }
        
        .ui-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #333;
            min-width: 250px;
            z-index: 1000;
        }
        
        .ui-panel h3 {
            margin-top: 0;
            color: #00ff00;
            text-align: center;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }
        
        .control-group input, .control-group select, .control-group button {
            width: 100%;
            padding: 8px;
            border: 1px solid #555;
            background: #222;
            color: #fff;
            border-radius: 4px;
        }
        
        .control-group button {
            background: #00ff00;
            color: #000;
            cursor: pointer;
            font-weight: bold;
            margin-top: 5px;
        }
        
        .control-group button:hover {
            background: #00cc00;
        }
        
        .laser-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #555;
            border-radius: 4px;
            padding: 10px;
            background: #111;
        }
        
        .laser-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px;
            margin-bottom: 5px;
            background: #333;
            border-radius: 3px;
        }
        
        .laser-item button {
            width: auto;
            padding: 3px 8px;
            background: #ff0000;
            font-size: 12px;
        }
        
        .laser-item button:hover {
            background: #cc0000;
        }
        
        .info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #333;
            color: #ccc;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <canvas id="renderCanvas"></canvas>
    
    <div class="ui-panel">
        <h3>🔴 Laser Control</h3>
        
        <div class="control-group">
            <label>Laser Farbe:</label>
            <select id="laserColor">
                <option value="red">Rot</option>
                <option value="green">Grün</option>
                <option value="blue">Blau</option>
                <option value="yellow">Gelb</option>
                <option value="purple">Lila</option>
            </select>
        </div>
        
        <div class="control-group">
            <label>Laser Intensität:</label>
            <input type="range" id="laserIntensity" min="0.1" max="2.0" step="0.1" value="1.0">
            <span id="intensityValue">1.0</span>
        </div>
        
        <div class="control-group">
            <button id="addLaserBtn">Laser hinzufügen</button>
            <button id="clearAllBtn">Alle löschen</button>
        </div>
        
        <div class="control-group">
            <label>Aktive Laser:</label>
            <div class="laser-list" id="laserList">
                <div style="text-align: center; color: #666;">Keine Laser aktiv</div>
            </div>
        </div>
    </div>
    
    <div class="info-panel">
        <strong>Steuerung:</strong><br>
        • Klicken Sie auf "Laser hinzufügen" und dann auf ein Objekt<br>
        • Mausrad: Zoom<br>
        • Rechte Maustaste + Ziehen: Kamera drehen<br>
        • Mittlere Maustaste + Ziehen: Kamera verschieben
    </div>
    
    <script src="https://cdn.babylonjs.com/babylon.js"></script>
    <script src="https://cdn.babylonjs.com/materialsLibrary/babylonjs.materials.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
